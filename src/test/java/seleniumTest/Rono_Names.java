package seleniumTest;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.BeforeTest;


import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;


import java.sql.*;
import java.util.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.io.BufferedReader;
import java.io.InputStreamReader;


import org.json.JSONArray;
import org.json.JSONObject;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class Rono_Names extends Rono_Messages {

    public static ChromeDriver driver;

    public static String url = "https://bigmumbaia.com/#/login";
    public static String phonenumberpath = "//input[@placeholder='Please enter the phone number']";
    public static String passwordpath = "(//input[@placeholder='Password'])[1]";
    public static String loginbutton = "(//button[text()='Log in'])[1]";

    public static String phoneNumber = "7012057241";
    public static String phoneNumber1 = "7012057241";
    //    public static String phoneNumber1 = "Bottleneck2143";
    public static String password = "Bottleneck2143";
    public static String password1 = "Deebiga9345890";
    public static String confirmButtonpath = "//div[text()='Confirm']";
    public static String wingo1 = "https://bigmumbaia.com/#/home/<USER>/WinTrx?typeId=13";
//    public static String wingo1 = "https://bigmumbai.life/#/home/<USER>/WinGo?typeId=1";


    private static final String tron_URL = "https://apilist.tronscanapi.com/api/block?sort=-balance&start=0&limit=1";

    public List<Integer> minutesToPrint_2_even = Arrays.asList(0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58);
    public List<Integer> minutesToPrint_2_odd = Arrays.asList(1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59);
    public List<Integer> minutesToPrint_3 = Arrays.asList(2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59);
    public List<Integer> minutesToPrint_5 = Arrays.asList(4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59);
    public List<Integer> minutesToPrint_10 = Arrays.asList(9, 19, 29, 39, 49, 59);
    public List<Integer> minutesToPrint_20 = Arrays.asList(19, 39, 59);
    public List<Integer> minutesToPrint_D1 = Arrays.asList(4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56);
    public List<Integer> minutesToPrint_D2 = Arrays.asList(0, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58);
    public List<Integer> minutesToPrint_D3 = Arrays.asList(3, 9, 15, 21, 27, 33, 39, 45, 51, 57);
    public List<Integer> minutesToPrint_D4 = Arrays.asList(1, 5, 7, 11, 13, 17, 19, 23, 25, 29, 31, 35, 37, 41, 43, 47, 49, 53, 55, 59);

    public int Balance;
    double balanceValue;
    public int previous_Balance = 100;
    public double previous_balanceValue = 100;
    public int inital_Balance = 35000;
    public int Balance_diff = 0;
    public int Balance_max = 0;

//    List<Integer> sequence = new ArrayList<>();

//    public static Map<Integer, Map<Integer, Integer>> transitions = new HashMap<>();
    public static int[] sequence = new int[1000]; // Adjust size as needed
    public static int sequenceLength = 0;

    int MARKOV_MEMORY_SIZE = 4;
    boolean SHOW_DEBUG_LOGS = true;

    // Internal state (managed automatically)
    Map<Integer, Map<Integer, Integer>> markovTransitions = new HashMap<>();
    int[] markovBuffer;
    int markovBufferPointer = 0;
    int totalBitsProcessed = 0;
    int count0 = 0;
    int count1 = 0;




    public int max_Balance;
    public String currentresult;
    public String currentresult_from_second;
    public String currentresult1;
    public String currentresult2;
    public String currentresult3;
    public String currentresult4;
    public String currentresult5;
    public String currentresult6;
    public String currentresult7;
    public String currentresult8;
    public String currentresult9;
    public String currentresult10;
    public String currentresult11;
    public String previousresult;
    public String previousresult_from_second;
    public String previousresult1 = "B";
    public String betresult = "B";
    public String compare = "B";
    public String blockHashLast6;

    public String Stanlee_string = "S";
    public int loop_count = 1;
    public int car_loop_count = 1;
    public int coco_loop_count = 1;
    public int bigHero6_loop_count = 1;
    public int wallE_loop_count = 1;
    public int mulan_loop_count = 1;
    public int hercules_loop_count = 1;
    public int kis1_loop_count = 1;
    public int padma_loop_count = 1;

    //    public String[] arr = {"B", "B", "S", "B", "S"};
//    public String[] arr = {"S", "S", "B", "B", "S"};
    public String[] arr = {"B", "B", "S"};
    public String[] arr1 = {"B", "S"};
    public int total = arr.length;
    public int index = 0;

    public int mark_rest = 0;


    public int total1 = arr1.length;
    public int index1 = 0;


    public int currentresult_number;
    public int previousresult_number;
    public int block_number;
    public int currentresult_3_number;

    public String currentresult_2;
    public static String resultsText;
    public String previousresult_2;

    public String currentresult_3;
    public String previousresult_3;
    public String betstring;

    public String currentresult_5;
    public String previousresult_5;

    public String currentresult_10;
    public String previousresult_10;

    public int samecount = 0;
    public int samecount_one = 0;
    public int diffcount_one = 0;
    public int diffcount = 0;

    public int bigHero6_diffcount = 0;
    public int bigHero6_samecount = 0;


    public int cars_same = 0;
    public int cars_diff = 0;

    public int Bsamecount = 0;
    public int Bdiffcount = 0;

    public int previousbetOneNo = 0;
    public int previousbetTwoNo = 0;
    public int previousbetFourNo = 0;

    public int MasterbetAmount = 1;
    public int Uni_MasterbetAmount = 1;
    public int anti_MasterbetAmount = 1;
    public int BMasterbetAmount = 1;
    public int restbetAmount = 1;

    public int inception_performed = 0;
    public int avatar_performed = 0;
    public int titanic_performed = 0;
    public int joker_performed = 0;
    public int gladiator_performed = 0;
    public int interstellar_performed = 0;
    public int matrix_performed = 0;
    public int avengers_performed = 0;
    public int thor_performed = 0;
    public int ironMan_performed = 0;
    public int blackPanther_performed = 0;
    public int endgame_performed = 0;
    public int inception2_performed = 0;
    public int frozen_performed = 0;
    public int tangled_performed = 0;
    public int up_performed = 0;
    public int moana_performed = 0;
    public int same1Perfromed = 0;
    public int cars_performed = 0;
    public int coco_performed = 0;
    public int ratatouille_performed = 0;
    public int toyStory_performed = 0;
    public int brave_performed = 0;
    public int shrek_performed = 0;
    public int kungFuPanda_performed = 0;
    public int zootopia_performed = 0;
    public int bigHero6_performed = 0;
    public int wallE_performed = 0;
    public int findingNemo_performed = 0;
    public int soul_performed = 0;
    public int encanto_performed = 0;
    public int aladdin_performed = 0;
    public int mulan_performed = 0;
    public int hercules_performed = 0;
    public int beautyAndBeast_performed = 0;
    public int lionKing_performed = 0;
    public int frozen2_performed = 0;
    public int sing_performed = 0;
    public int minions_performed = 0;
    public int madagascar_performed = 0;
    public int despicableMe_performed = 0;
    public int iceAge_performed = 0;
    public int cloudyMeatballs_performed = 0;
    public int emojiMovie_performed = 0;
    public int smurfs_performed = 0;
    public int hotelTransylvania_performed = 0;
    public int megamind_performed = 0;
    public int spiderman_performed = 0;
    public int batman_performed = 0;
    public int superman_performed = 0;
    public int aquaman_performed = 0;
    public int wonderWoman_performed = 0;
    public int justiceLeague_performed = 0;
    public int harshini_performed = 0;
    public int divi3_performed = 0;
    public int tonystrak_performed = 0;
    public int betone_performed = 0;
    public int elon_performed = 0;
    public int steveJobs_performed = 0;
    public int harshini_performed1 = 0;
    public int killBill_performed = 0;
    public int reservoirDogs_performed = 0;
    public int hatefulEight_performed = 0;
    public int django_performed = 0;
    public int pulpFiction_performed = 0;
    public int godfather_performed = 0;
    public int goodfellas_performed = 0;
    public int laLaLand_performed = 0;
    public int whiplash_performed = 0;
    public int parasite_performed = 0;
    public int dunkirk_performed = 0;
    public int tenet_performed = 0;
    public int dune_performed = 0;
    public int lordRings_performed = 0;
    public int hobbit_performed = 0;


    public int inception_samecount = 0;
    public int avatar_samecount = 0;
    public int titanic_samecount = 0;
    public int joker_samecount = 0;
    public int gladiator_samecount = 0;
    public int interstellar_samecount = 0;
    public int matrix_samecount = 0;
    public int avengers_samecount = 0;
    public int thor_samecount = 0;
    public int ironMan_samecount = 0;
    public int blackPanther_samecount = 0;
    public int endgame_samecount = 0;
    public int inception2_samecount = 0;
    public int frozen_samecount = 0;
    public int tangled_samecount = 0;
    public int up_samecount = 0;
    public int moana_samecount = 0;

    public int cars_samecount = 0;
    public int coco_samecount = 0;
    public int ratatouille_samecount = 0;
    public int toyStory_samecount = 0;
    public int brave_samecount = 0;
    public int shrek_samecount = 0;
    public int kungFuPanda_samecount = 0;
    public int zootopia_samecount = 0;
    public int zootopia_diffcount = 0;

    public int wallE_samecount = 0;
    public int findingNemo_samecount = 0;
    public int soul_samecount = 0;
    public int encanto_samecount = 0;
    public int aladdin_samecount = 0;
    public int mulan_samecount = 0;
    public int hercules_samecount = 0;
    public int beautyAndBeast_samecount = 0;
    public int lionKing_samecount = 0;
    public int frozen2_samecount = 0;
    public int sing_samecount = 0;
    public int minions_samecount = 0;
    public int madagascar_samecount = 0;
    public int despicableMe_samecount = 0;
    public int iceAge_samecount = 0;
    public int cloudyMeatballs_samecount = 0;
    public int emojiMovie_samecount = 0;
    public int smurfs_samecount = 0;
    public int hotelTransylvania_samecount = 0;
    public int megamind_samecount = 0;
    public int spiderman_samecount = 0;
    public int batman_samecount = 0;
    public int superman_samecount = 0;
    public int aquaman_samecount = 0;
    public int wonderWoman_samecount = 0;
    public int justiceLeague_samecount = 0;
    public int harshini_samecount = 0;
    public int killBill_samecount = 0;
    public int killBill_diffcount = 0;
    public int reservoirDogs_samecount = 0;
    public int parasite_samecount = 0;
    public int reservoirDogs_diffcount = 0;
    public int dune_samecount = 0;
    public int dune_diffcount = 0;
    public int tenet_samecount = 0;
    public int tenet_diffcount = 0;
    public int lordRings_samecount = 0;
    public int lordRings_diffcount = 0;
    public int brave_diffcount = 0;
    public int shrek_diffcount = 0;
    public int kungFuPanda_diffcount = 0;
    public int parasite_diffcount = 0;
    public int laLaLand_samecount = 0;
    public int whiplash_samecount = 0;
    public int goodfellas_samecount = 0;
    public int godfather_samecount = 0;
    public int pulpFiction_samecount = 0;
    public int pulpFiction_diffcount = 0;
    public int godfather_diffcount = 0;
    public int whiplash_diffcount = 0;
    public int same_string_samecount = 0;
    public int same_string_diffcount = 0;


    public int laLaLand_amount = 1;


    public int laLaLand_diffcount = 0;
    public int dunkirk_samecount = 0;
    public int dunkirk_diffcount = 0;
    public int django_samecount = 0;

    public int django_diffcount = 0;
    public int hatefulEight_samecount = 0;
    public int hatefulEight_diffcount = 0;
    public int hobbit_diffcount = 0;
    public int divi3_diffcount = 0;
    public int tonystrak_diffcount = 0;
    public int betone_diffcount = 0;
    public int elon_diffcount = 0;
    public int harshini_diffcount1 = 0;
    public int steveJobs_diffcount = 0;
    public int mulan_diffcount = 0;
    public int hercules_diffcount = 0;
    public int beautyAndBeast_diffcount = 0;
    public int lionKing_diffcount = 0;
    public int frozen2_diffcount = 0;
    public int sing_diffcount = 0;
    public int minions_diffcount = 0;
    public int madagascar_diffcount = 0;
    public int despicableMe_diffcount = 0;
    public int iceAge_diffcount = 0;
    public int cloudyMeatballs_diffcount = 0;
    public int emojiMovie_diffcount = 0;
    public int smurfs_diffcount = 0;
    public int hotelTransylvania_diffcount = 0;
    public int megamind_diffcount = 0;
    public int spiderman_diffcount = 0;
    public int batman_diffcount = 0;
    public int superman_diffcount = 0;
    public int aquaman_diffcount = 0;
    public int wonderWoman_diffcount = 0;
    public int justiceLeague_diffcount = 0;
    public int harshini_diffcount = 0;




    public int hobbit_samecount = 0;
    public int divi3_samecount = 0;
    public int tonystrak_samecount = 0;
    public int betone_samecount = 0;
    public int elon_samecount = 0;
    public int harshini_samecount1 = 0;


    public int inception_diffcount = 0;
    public int avatar_diffcount = 0;
    public int titanic_diffcount = 0;
    public int joker_diffcount = 0;
    public int gladiator_diffcount = 0;
    public int interstellar_diffcount = 0;
    public int matrix_diffcount = 0;
    public int avengers_diffcount = 0;
    public int thor_diffcount = 0;
    public int ironMan_diffcount = 0;
    public int blackPanther_diffcount = 0;
    public int endgame_diffcount = 0;
    public int inception2_diffcount = 0;
    public int frozen_diffcount = 0;
    public int tangled_diffcount = 0;
    public int up_diffcount = 0;
    public int moana_diffcount = 0;

    public int cars_diffcount = 0;
    public int coco_diffcount = 0;
    public int ratatouille_diffcount = 0;
    public int toyStory_diffcount = 0;


    public int wallE_diffcount = 0;
    public int findingNemo_diffcount = 0;
    public int soul_diffcount = 0;
    public int encanto_diffcount = 0;
    public int aladdin_diffcount = 0;




    public int inception_amount = 1;
    public int cloudyMeatballs_amount = 1;
    public int avatar_amount = 1;
    public int titanic_amount = 1;
    public int joker_amount = 1;
    public int gladiator_amount = 1;
    public int interstellar_amount = 1;
    public int matrix_amount = 1;
    public int emojiMovie_amount = 1;
    public int avengers_amount = 1;
    public int thor_amount = 1;
    public int hotelTransylvania_amount = 1;
    public int ironMan_amount = 1;
    public int madagascar_amount = 1;
    public int blackPanther_amount = 1;
    public int endgame_amount = 1;
    public int inception2_amount = 1;
    public int frozen_amount = 1;
    public int tangled_amount = 1;
    public int up_amount = 1;
    public int spiderman_amount = 1;
    public int moana_amount = 1;
    public int same_string_betamount = 1;
    public int cars_amount = 1;
    public int coco_amount = 1;
    public int ratatouille_amount = 1;
    public int despicableMe_amount = 1;
    public int toyStory_amount = 1;
    public int brave_amount = 1;
    public int shrek_amount = 1;
    public int kungFuPanda_amount = 1;
    public int zootopia_amount = 1;
    public int bigHero6_amount = 1;
    public int justiceLeague_amount = 1;
    public int harshini_amount = 1;
    public int divi3_amount = 1;
    public int tonystrak_amount = 1;
    public int elon_amount = 1;
    public int steveJobs_amount = 1;
    public int harshini_amount1 = 1;
    public int wallE_amount = 1;
    public int findingNemo_amount = 1;
    public int soul_amount = 1;
    public int encanto_amount = 1;
    public int aladdin_amount = 1;
    public int mulan_amount = 1;
    public int hercules_amount = 1;
    public int beautyAndBeast_amount = 1;
    public int lionKing_amount = 1;
    public int frozen2_amount = 1;
    public int sing_amount = 1;
    public int minions_amount = 1;
    public int iceAge_amount = 1;
    public int smurfs_amount = 1;
    public int megamind_amount = 1;
    public int batman_amount = 1;
    public int superman_amount = 1;
    public int aquaman_amount = 1;
    public int wonderWoman_amount = 1;
    public int hobbit_amount = 1;
    public int lordRings_amount = 1;
    public int dune_amount = 1;
    public int tenet_amount = 1;
    public int dunkirk_amount = 1;
    public int parasite_amount = 1;

    public int godfather_amount = 1;
    public int goodfellas_amount = 1;
    public int whiplash_amount = 1;
    public int pulpFiction_amount = 1;
    public int django_amount = 1;
    public int hatefulEight_amount = 1;
    public int reservoirDogs_amount = 1;
    public int killBill_amount = 1;


    public String what_string;
    public String same_string = "B";
    public String result_db = "B";
    public String wonderWoman_string = "B";
    public String justiceLeague_string = "B";
    public String harshini_string = "B";
    public String divi3_string = "B";
    public String tonystrak_string = "B";
    public String elon_string = "B";
    public String harshini_string1 = "B";
    public String aquaman_string = "B";
    public String superman_string = "B";
    public String batman_string = "B";
    public String spiderman_string = "B";
    public String megamind_string = "B";
    public String hotelTransylvania_string = "B";
    public String smurfs_string = "B";
    public String emojiMovie_string = "B";
    public String cloudyMeatballs_string = "B";
    public String despicableMe_string = "B";
    public String iceAge_string = "B";
    public String minions_string = "B";
    public String madagascar_string = "B";
    public String sing_string = "B";
    public String frozen2_string = "B";
    public String hercules_string = "B";
    public String lionKing_string = "B";
    public String beautyAndBeast_string = "B";
    public String mulan_string = "B";
    public String aladdin_string = "B";
    public String encanto_string = "B";
    public String findingNemo_string = "B";
    public String soul_string = "B";
    public String wallE_string = "B";
    public String bigHero6_string = "B";
    public String brave_string = "B";
    public String zootopia_string = "B";
    public String steveJobs_string = "B";
    public String kungFuPanda_string = "B";
    public String shrek_string = "B";
    public String cars_string = "B";
    public String toyStory_string = "B";
    public String ratatouille_string = "B";
    public String coco_string = "B";
    public String inception_string = "S";
    public String avatar_string = "S";
    public String titanic_string = "S";
    public String joker_string = "S";
    public String gladiator_string = "S";
    public String interstellar_string = "S";
    public String matrix_string = "S";
    public String avengers_string = "S";
    public String thor_string = "S";
    public String ironMan_string = "S";
    public String blackPanther_string = "S";
    public String hobbit_string = "S";
    public String lordRings_string = "S";
    public String dune_string = "S";
    public String tenet_string = "S";
    public String dunkirk_string = "S";
    public String parasite_string = "S";
    public String laLaLand_string = "S";
    public String whiplash_string = "S";
    public String goodfellas_string = "S";
    public String godfather_string = "S";
    public String pulpFiction_string = "S";
    public String django_string = "S";
    public String hatefulEight_string = "S";
    public String reservoirDogs_string = "S";
    public String killBill_string = "S";

    public String endgame_string = "S";
    public String inception2_string = "S";
    public String frozen_string = "S";
    public String tangled_string = "S";
    public String up_string = "S";
    public String moana_string = "S";
    public String emoji;
    public String emoji1;

    public int last_number;

    public int new_betAmount1 = 1;


    public int new_betAmount2 = 2;
    public int new_betAmount3 = 1;
    public int new_betAmount4 = 1;
    public int new_betAmount5 = 1;
    public int new_betAmount6 = 1;
    public int new_betAmount7 = 5;
    public int put_amount = 2;

    public int increase_amount1 = 0;
    public int increase_amount2 = 0;
    public int increase_amount3 = 0;
    public int increase_amount4 = 0;
    public int increase_amount5 = 0;
    public int increase_amount6 = 0;
    public int increase_amount7 = 0;


    public int intial_balanace = 2000;
    public int intial_balanace1 = 2000;
    public int intial_balanace2 = 2000;
    public int intial_balanace3 = 2000;
    public int intial_balanace4 = 2000;
    public int intial_balanace5 = 2000;
    public int intial_balanace6 = 2000;
    public int intial_balanace7 = 2000;


    public int previous_increase_amount = 0;
    public int previous_increase_amount1 = 0;
    public int previous_increase_amount2 = 0;
    public int previous_increase_amount3 = 0;
    public int previous_increase_amount4 = 0;
    public int previous_increase_amount5 = 0;
    public int previous_increase_amount6 = 0;
    public int previous_increase_amount7 = 0;

    public int previous_suresh_amount = 0;
    public int previous_suresh_amount1 = 0;
    public int previous_suresh_amount2 = 0;
    public int previous_suresh_amount3 = 0;
    public int previous_suresh_amount4 = 0;
    public int previous_suresh_amount5 = 0;
    public int previous_suresh_amount6 = 0;
    public int previous_suresh_amount7 = 0;

    public int previous_1 = 1;
    public int previous_2 = 2;
    public int previous_4 = 4;
    public int previous_8 = 8;
    public int previous_16 = 16;
    public int previous_32 = 32;
    public int previous_64 = 64;
    public int previous_128 = 128;
    public int previous_256 = 256;
    public int previous_512 = 512;

    public int one = 0;
    public int two = 0;
    public int four = 0;
    public int eight = 0;
    public int sixteen = 0;
    public int thirtytwo = 0;
    public int sixtyfour = 0;
    public int onetwentyeight = 0;
    public int twofixsix = 0;
    public int fivetwelve = 0;





    public int obuli_amount = 1;
    public int amount_divisible = 128;
    public String obuli_string;


    public static final String JDBC_URL = "********************************************************************";
    public static final String DB_USERNAME = "rono";
    public static final String DB_PASSWORD = "t2g4zJ[xab6RflYh";

    public void getresult_from_second() {
        int resultindex = 2;
        int previousIndex = 3;

        currentresult_from_second = getElementText("(//div[@class='van-col van-col--5']//span)[1]");
        previousresult_from_second = getElementText("(//div[@class='van-col van-col--5']//span)[3]");

        if (currentresult_from_second.equals("Small")) {
            currentresult_from_second = "S";
        } else {
            currentresult_from_second = "B";
        }


        if (previousresult_from_second.equals("Small")) {

            previousresult_from_second = "S";
        } else {
            previousresult_from_second = "B";
        }
    }

    public void getresult() {
        int resultindex = 2;
        int previousIndex = 3;

        currentresult = getElementText("(//div[@class='van-col van-col--5']//span)[1]");
        previousresult = getElementText("(//div[@class='van-col van-col--5']//span)[2]");


        if (currentresult.equals("Small")) {
            currentresult = "S";
        } else {
            currentresult = "B";
        }


        if (previousresult.equals("Small")) {

            previousresult = "S";
        } else {
            previousresult = "B";
        }
    }

    public static char getRandomBOrSOrAOrD() {
        Random random = new Random();
        int choice = random.nextInt(4);
        return choice == 0 ? 'B' : choice == 1 ? 'S' : choice == 2 ? 'S' : 'B';
    }

    public static char getRandomChoice() {
        try {
            // Get the current time in milliseconds
            long currentTime = Instant.now().toEpochMilli();

            // Convert to byte array for hashing
            byte[] timeBytes = Long.toString(currentTime).getBytes();

            // Hash using SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(timeBytes);

            // Convert first 4 bytes into an integer
            int hashValue = ((hash[0] & 0xFF) << 24) | ((hash[1] & 0xFF) << 16) | ((hash[2] & 0xFF) << 8) | (hash[3] & 0xFF);

            // Use hash-based random number generation
            Random random = new Random(hashValue);
            int choice = random.nextInt(4);  // Generates 0,1,2,3

            // Return 'B' or 'S' based on choice
            return (choice == 0) ? 'B' : 'S';

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return 'B';  // Default fallback
        }
    }

    public void on_flow_value_check(int amount, String what) throws InterruptedException {

        obuli_amount = amount;
        obuli_string = what;
        //---------------------------------------------------------------------------------------
        if (obuli_amount == 1) {
            new_betAmount1 = obuli_amount + increase_amount1;
            increase_amount1++;
        } else if (obuli_amount > 1) {
            increase_amount1 = 0;
            new_betAmount1 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace1 = intial_balanace1 - new_betAmount1;
        } else if (obuli_amount == 2) {
            intial_balanace1 = intial_balanace1 + (obuli_amount * previous_increase_amount1);
        }
//----------------------------------------------------------------------------------------

        if (obuli_amount == 1) {
            new_betAmount2 = 2 + increase_amount2;
            increase_amount2 += 2;
        } else if (obuli_amount > 2) {
            increase_amount2 = 0;
            new_betAmount2 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace2 = intial_balanace2 - new_betAmount2;
        } else if (obuli_amount == 4) {
            intial_balanace2 = intial_balanace2 + (obuli_amount * previous_increase_amount2);
        }

//----------------------------------------------------------------------------------------

        if (obuli_amount == 1) {
            new_betAmount3 = obuli_amount + increase_amount3;
            increase_amount3++;
        } else if (obuli_amount > 4) {
            increase_amount3 = 0;
            new_betAmount3 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace3 = intial_balanace3 - new_betAmount3;
        } else if (obuli_amount == 8) {
            intial_balanace3 = intial_balanace3 + (obuli_amount * previous_increase_amount3);
        }

//----------------------------------------------------------------------------------------


        if (obuli_amount == 1) {
            new_betAmount4 = obuli_amount + increase_amount4;
            increase_amount4++;
        } else if (obuli_amount > 8) {
            increase_amount4 = 0;
            new_betAmount4 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace4 = intial_balanace4 - new_betAmount4;
        } else if (obuli_amount == 16) {
            intial_balanace4 = intial_balanace4 + (obuli_amount * previous_increase_amount4);
        }

//----------------------------------------------------------------------------------------

        if (obuli_amount == 1) {
            new_betAmount5 = obuli_amount + increase_amount5;
            increase_amount5++;
        } else if (obuli_amount > 16) {
            increase_amount5 = 0;
            new_betAmount5 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace5 = intial_balanace5 - new_betAmount5;
        } else if (obuli_amount == 32) {
            intial_balanace5 = intial_balanace5 + (obuli_amount * previous_increase_amount5);
        }

//----------------------------------------------------------------------------------------


        if (obuli_amount == 1) {
            new_betAmount6 = obuli_amount + increase_amount6;
            increase_amount6++;
        } else if (obuli_amount > 32) {
            increase_amount6 = 0;
            new_betAmount6 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace6 = intial_balanace6 - new_betAmount6;
        } else if (obuli_amount == 64) {
            intial_balanace6 = intial_balanace6 + (obuli_amount * previous_increase_amount6);
        }

//----------------------------------------------------------------------------------------

        if (obuli_amount == 1) {
            new_betAmount7 = 2 + increase_amount7;
            increase_amount7++;
        } else if (obuli_amount > 64) {
            increase_amount7 = 0;
            new_betAmount7 = 0;
        }

        if (obuli_amount == 1) {
            intial_balanace7 = intial_balanace7 - new_betAmount7;
        } else if (obuli_amount == 128) {
            intial_balanace7 = intial_balanace7 + (obuli_amount * previous_increase_amount7);
        }


        if (obuli_amount < 8) {
//            int byTwo;
//            if (new_betAmount2 > 1) {
//                byTwo = new_betAmount2 / 2;
//            } else {
//                byTwo = new_betAmount2;
//            }

//            sendMessageRamu3("------------------------------------");
//            performBet(opposite_string(obuli_string), byTwo * obuli_amount);
//            sendMessageRamu3("Amount3 => "+String.valueOf(byTwo)+" ==> "+String.valueOf(byTwo * obuli_amount));
//            sendMessageDivi5("---------------------------------------------");
//            sendMessageDivi5("Cur => " + Balance + " => " + obuli_string + " => " +String.valueOf(byTwo * obuli_amount));
//            performBet(opposite_string(obuli_string), new_betAmount4 * obuli_amount);
        }
//        sendMessageDivi5(String.valueOf(new_betAmount3));
//        if (new_betAmount4 > 19) {
//            if (obuli_amount < 32) {
//                performBet(opposite_string(obuli_string), (new_betAmount4) * obuli_amount);
//                sendMessageDivi5("---------------------------------------------");
//                sendMessageDivi5("Cur => " + Balance + " => " + obuli_string + " => " + (new_betAmount4) * obuli_amount);
//            }
//
//        }

        if (new_betAmount1 > 0 || new_betAmount2 > 0 || new_betAmount3 > 0 || new_betAmount4 > 0 || new_betAmount5 > 0 || new_betAmount6 > 0 || new_betAmount7 > 0) {

            if (new_betAmount1 > 0) {
//                sendMessageRamu1("R1 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount1 + " => " + intial_balanace1);
            }
            if (new_betAmount2 > 0) {
//                sendMessageRamu6("R2 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount2 + " => " + intial_balanace2);
//                performBet(opposite_string(obuli_string), (new_betAmount2 * obuli_amount));
//                int temp = (new_betAmount2 * obuli_amount);
////                sendMessageDivi10("Prev => " + Balance);
//                sendMessageDivi10("------------------");
//                sendMessageDivi10("Obuli => " + String.valueOf(obuli_amount));
//                sendMessageDivi10("new => " + String.valueOf(new_betAmount2));
//                sendMessageDivi10(String.valueOf(temp));


            }
            if (new_betAmount3 > 0) {
//                sendMessageRamu3("R3 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount3 + " => " + intial_balanace3);

            }
            if (new_betAmount4 > 0) {
//                performBet(opposite_string(obuli_string), new_betAmount4 * obuli_amount);
//                sendMessageRamu4("R4 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount4 + " => " + intial_balanace4);

            }
            if (new_betAmount5 > 0) {
//                sendMessageRamu4("R5 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount5 + " => " + intial_balanace5);
            }
            if (new_betAmount6 > 0) {
//                sendMessageRamu5("R6 =>" + obuli_string + "  =>" + obuli_amount + " => " + new_betAmount6 + " => " + intial_balanace6);
            }
            if (new_betAmount7 > 0) {

                int new_betAmount_addition = new_betAmount7;
                String opposite = opposite_string(obuli_string);

                sendMessageRamu6("R7 =>" + opposite + "  =>" + obuli_amount + " => " + new_betAmount_addition + " => " + intial_balanace7);

                if (obuli_amount < amount_divisible) {
                    int betAmount = new_betAmount_addition * obuli_amount;
                    performBet(opposite, betAmount);
                    sendMessageRamu6("In Loop --- " + betAmount);
                } else {
                    int factor = obuli_amount / amount_divisible;
                    int betAmount = new_betAmount_addition * factor;
                    performBet(opposite, betAmount);
                    sendMessageRamu6("Divi Loop --- " + betAmount);
                }
            }


        }

        previous_increase_amount1 = increase_amount1;
        previous_increase_amount2 = increase_amount2;
        previous_increase_amount3 = increase_amount3;
        previous_increase_amount4 = increase_amount4;
        previous_increase_amount5 = increase_amount5;
        previous_increase_amount6 = increase_amount6;
        previous_increase_amount7 = increase_amount7;
//----------------------------------------------------------------------------------------


    }


    public void getresult1() {
        int resultindex = 2;
        int previousIndex = 3;
        int betIndex = 4;
        currentresult = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        currentresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[1]"));
        previousresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[1]"));
        previousresult = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        betresult = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");

//        block_number = Integer.parseInt(driver.findElementByXPath("(//div[@class='van-col van-col--4'])[3]").getText());
        block_number = block_number + 5;



        emoji1 = (currentresult_number % 2 != 0) ? "🟢" : "🔴";

//
//        int add_number = currentresult_number + previousresult_number;
//
//        System.out.println(add_number + "=>" + currentresult_number + "=>" + previousresult_number + "=>" + add_number);
//        if (add_number < 5) {
//            betresult = "S";
//        } else {
//            int result = splitAndSum(add_number);
//            if (result < 5) {
//                betresult = "S";
//            } else {
//                betresult = "B";
//            }
//            System.out.println("betresult" + "=> " + result + "=> " + betresult);
//
//        }
    }

//    public void getresult1() {
//        int resultindex = 2;
//        int previousIndex = 3;
//        int betIndex = 4;
//
//        currentresult = getElementText("(//div[contains(@class,'van-col van-col--5')]/following-sibling::div)[3]");
//
//        if (currentresult.equals("Small")){
//            currentresult = "S";
//        }else{
//            currentresult = "B";
//        }
////        System.out.println("compare =>" + betresult);
//
////        currentresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[1]"));
////        previousresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[1]"));
////        previousresult = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
////        betresult = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");
////
////        block_number = Integer.parseInt(driver.findElementByXPath("(//div[@class='van-col van-col--4'])[3]").getText());
////        block_number = block_number + 5;
////        System.out.println(block_number);
////
////
////        emoji1 = (currentresult_number % 2 != 0) ? "🟢" : "🔴";
//

    /// /
    /// /        int add_number = currentresult_number + previousresult_number;
    /// /
    /// /        System.out.println(add_number + "=>" + currentresult_number + "=>" + previousresult_number + "=>" + add_number);
    /// /        if (add_number < 5) {
    /// /            betresult = "S";
    /// /        } else {
    /// /            int result = splitAndSum(add_number);
    /// /            if (result < 5) {
    /// /                betresult = "S";
    /// /            } else {
    /// /                betresult = "B";
    /// /            }
    /// /            System.out.println("betresult" + "=> " + result + "=> " + betresult);
    /// /
    /// /        }
//    }
    public static int splitAndSum(int number) {
        if (number >= 10 && number <= 99) { // If it's a two-digit number
            int firstDigit = number / 10;  // Extract the first digit
            int secondDigit = number % 10; // Extract the second digit
            return firstDigit + secondDigit;
        } else {
            return number; // Return the number itself if it's not two-digit
        }
    }

    public void getresult_previous() {
        int resultindex = 2;
        int previousIndex = 3;
        int betIndex = 4;
        currentresult = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        previousresult = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        betresult = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");


    }

    public void getresult_data() {
        int resultindex = 2;
        int previousIndex = 5;
        int betIndex = 4;

        currentresult1 = getElementText("(//div[@class='van-row'])[2]/div[5]/div/div[2]");
        currentresult2 = getElementText("(//div[@class='van-row'])[3]/div[5]/div/div[2]");
        currentresult3 = getElementText("(//div[@class='van-row'])[4]/div[5]/div/div[2]");
        currentresult4 = getElementText("(//div[@class='van-row'])[5]/div[5]/div/div[2]");
        currentresult5 = getElementText("(//div[@class='van-row'])[6]/div[5]/div/div[2]");
        currentresult6 = getElementText("(//div[@class='van-row'])[7]/div[5]/div/div[2]");
        currentresult7 = getElementText("(//div[@class='van-row'])[8]/div[5]/div/div[2]");
        currentresult8 = getElementText("(//div[@class='van-row'])[9]/div[5]/div/div[2]");
        currentresult9 = getElementText("(//div[@class='van-row'])[10]/div[5]/div/div[2]");
        currentresult10 = getElementText("(//div[@class='van-row'])[11]/div[5]/div/div[2]");


    }


    public void getresult_2_min() {
        int resultindex = 3;
        int previousIndex = 5;


        currentresult_2 = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        previousresult_2 = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        currentresult = currentresult_2;
        previousresult = previousresult_2;
    }

    public static String fetchFilteredResults(String searchPattern) {
        System.out.println("Into DB function");

        String sql = "SELECT * FROM block_results WHERE results_text LIKE ? ORDER BY id DESC LIMIT 1";

        try (Connection connection = DriverManager.getConnection(JDBC_URL, DB_USERNAME, DB_PASSWORD); PreparedStatement pstmt = connection.prepareStatement(sql)) {

            pstmt.setString(1, searchPattern + "%"); // Ensure pattern matching

            try (ResultSet resultSet = pstmt.executeQuery()) {
                if (resultSet.next()) { // Use if since LIMIT 1 returns only one row
                    int id = resultSet.getInt("id");
                    resultsText = resultSet.getString("results_text");

                    // Print the fetched records
                    System.out.println("ID: " + id + " | Results Text: " + resultsText);
                } else {
                    System.out.println("No matching results found.");
                }
            }
        } catch (SQLException e) {
            System.err.println("Database query error: " + e.getMessage());
            e.printStackTrace();
        }
        return resultsText;
    }


    public void performBet(String type, double amount) throws InterruptedException {
        Thread.sleep(2000);
        String typeXPath;
        if (type.equals("B")) {
            typeXPath = "//div[normalize-space(text())='Big']";
        } else {
            typeXPath = "//div[text()='Big']/following-sibling::div";
        }


        clickbutton(typeXPath);
        WebElement inputField = driver.findElement(By.xpath("//input[@type='number']"));
        inputField.clear();
        inputField.sendKeys(Keys.BACK_SPACE);
        String updatedBetAmountString = Integer.toString((int) amount);
        driver.findElement(By.xpath("//input[@type='number']")).sendKeys(updatedBetAmountString);
        Thread.sleep(2000);
        driver.findElement(By.xpath("//div[text()='Cancel']/following-sibling::div")).click();
//System.out.println((type.equals("B") ? "Big" : "Small") + " Clicked with amount " + amount);
//sendMessage("Bet placed: " + (type.equals("B") ? "Big" : "Small") + " with amount " + amount);
    }

    public String opposite_string(String currentresult) {
        if (currentresult.equals("B")) {
            currentresult = "S";
        } else {
            currentresult = "B";
        }
        return currentresult;

    }

    public void getresult_3_min() {
        int resultindex = 3;
        int previousIndex = 11;
        int betIndex = 10;

        currentresult_3 = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        previousresult_3 = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        betstring = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");
        currentresult_3_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[1]"));

//        currentresult = currentresult_3;
//        previousresult = previousresult_3;

        emoji = (currentresult_3_number % 2 != 0) ? "🟢" : "🔴";


    }

    public void getresult_5_min() {
        int resultindex = 6;
        int previousIndex = 11;

        currentresult_5 = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        previousresult_5 = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");

    }


    public static void clickbutton(String xpath) throws InterruptedException {

        WebElement element = new WebDriverWait(driver, 20).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
//        Thread.sleep(2000);
        element.click();
        Thread.sleep(1000);
    }

    public static String getElementText(String xpath) {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        return element.getText();
    }

    public static void sendKeysToElement(String xpath, String keys) throws InterruptedException {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        Thread.sleep(1000);
        element.sendKeys(keys);
    }

    public int get_block_number(int block_number1) {
        while (true) {
            try {
                long startTime = System.currentTimeMillis();

                // Make the HTTP request
                HttpURLConnection connection = (HttpURLConnection) new URL(tron_URL).openConnection();
                connection.setRequestMethod("GET");

                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    // Parse JSON response
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    JSONArray dataArray = jsonResponse.optJSONArray("data");

                    if (dataArray != null && dataArray.length() > 0) {
                        JSONObject blockData = dataArray.getJSONObject(0);
                        int number = blockData.optInt("number", -1);
                        String blockHash = blockData.optString("hash", "N/A");

                        // Get last 6 characters of block hash
                        blockHashLast6 = blockHash.length() >= 6 ? blockHash.substring(blockHash.length() - 6) : "N/A";

                        // Get current time in HH:mm:ss format
                        String currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

                        if (number > block_number1) {
                            blockHashLast6 = "934589";
                            System.out.println("Current Time: " + currentTime + ", 'number': " + number + ", 'block': " + blockHashLast6);
                            System.out.println("alternative.........");

                            return number; // Return the block number when found
                        } else if (number == block_number1) {
                            System.out.println("Current Time: " + currentTime + ", 'number': " + number + ", 'block': " + blockHashLast6);
                            System.out.println("Wait.........");

                            return number;
                        }
                        System.out.println("Current Time: " + currentTime + ", 'number': " + number + ", 'block': " + blockHashLast6);
                    }
                } else {
                    System.out.println("Error: Received non-200 status code " + responseCode);
                }

                long elapsedTime = System.currentTimeMillis() - startTime;
                long waitTime = Math.max(0, 1000 - elapsedTime);
                Thread.sleep(waitTime);

            } catch (Exception e) {
                System.out.println("Request failed: " + e.getMessage());
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    public static int getLastNumber(String str) {
        for (int i = str.length() - 1; i >= 0; i--) {
            if (Character.isDigit(str.charAt(i))) {
                return Character.getNumericValue(str.charAt(i)); // Convert char to actual number
            }
        }
        return -1; // Return -1 if no number is found
    }

    public int getBalance() {
        WebElement balanceElement = driver.findElement(By.xpath("(//div[@class='Wallet__C-balance']//div)[1]"));
        String balanceText = balanceElement.getText().replace("₹", "").replace(",", "");
        balanceValue = Double.parseDouble(balanceText);
        Balance = (int) Math.round(balanceValue);
        return Balance;
    }

    public static int findPreviousValue1(int value2) {
        // List of value pairs as a one-dimensional array of arrays
        int[][] values = {{2,77000},
                {2,77002},
                {2,77004},
                {2,77006},
                {4,77008},
                {4,77012},
                {4,77015},
                {4,77019},
                {8,77023},
                {8,77031},
                {8,77038},
                {8,77046},
                {16,77054},
                {16,77069},
                {16,77084},
                {16,77100},
                {32,77115},
                {32,77146},
                {32,77177},
                {32,77207},
                {64,77238},
                {64,77300},
                {64,77361},
                {64,77422},
                {128,77484},
                {128,77607},
                {128,77730},
                {128,77852},
                {256,77975},
                {256,78221},
                {256,78467},
                {256,78713},
                {512,78958},
                {512,79450},
                {512,79941},
                {512,80433},
                {1024,80924},
                {1024,81908},
                {1024,82891},
                {1024,83874},
                {2048,84857},
                {2048,86823},
                {2048,88789},
                {2048,90755},
                {4096,92721},
                {4096,96653},
                {4096,100585},
                {4096,104517},
                {8192,108450},
                {8192,116314},
                {8192,124178},
                {8192,132043},
                {16384,139907},
                {16384,155636},
                {16384,171364},
                {16384,187093},
                {32768,202821},
                {32768,234279},
                {32768,265736},
                {32768,297193},
                {65536,328651},
                {65536,391565},
                {65536,454480},
                {65536,517394},
                {131072,580309},
                {131072,706138},
                {131072,831967},
                {131072,957796},



        };

        // Use binary search to find the index where value2 would be inserted
        int index = Arrays.binarySearch(values, new int[]{0, value2}, (a, b) -> Integer.compare(a[1], b[1]));

        // Adjust the index to get the previous value1
        int prevValue1;
        if (index >= 0) {
            // If value2 is found exactly in the array
            prevValue1 = values[index][0];
        } else {
            int insertionPoint = -index - 1;
            if (insertionPoint > 0) {
                prevValue1 = values[insertionPoint - 1][0];
            } else {
                prevValue1 = -1; // No previous value1 found
            }
        }

        return prevValue1;
    }

    public boolean isInternetAvailable() {
        try {
            URL url = new URL("https://www.google.com");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            int responseCode = connection.getResponseCode();
            return (200 <= responseCode && responseCode <= 399);
        } catch (IOException e) {
            return false;
        }
    }

    public void waitForInternet() {
//        System.out.println("Checking internet connectivity...");
        while (!isInternetAvailable()) {
//            System.out.println("Internet not available. Retrying in 10 seconds...");
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
//        System.out.println("Internet is back. Proceeding...");
    }

    public static int findClosestN(int sum) {
        double n = (-1 + Math.sqrt(1 + 8 * sum)) / 2;
        return (int) Math.floor(n); // take the floor to get the lowest valid n
    }



    @BeforeTest
    public static void setup() {
        WebDriverManager.chromedriver().setup();
        ChromeOptions chromeOptions = new ChromeOptions();
        chromeOptions.addArguments("--no-sandbox");
//        chromeOptions.addArguments("--headless");
        chromeOptions.addArguments("disables-gpu");
        chromeOptions.addArguments("--mute-audio");
        driver = new ChromeDriver(chromeOptions);
        driver.manage().window().maximize();
    }

    static int predictNext(List<Integer> sequence) {
        if (sequence.isEmpty()) {
            return 1; // Default if empty
        }

        int size = sequence.size();

        // --- Check for Arithmetic Sequence (+N each step) ---
        if (size >= 2) {
            int diff = sequence.get(size - 1) - sequence.get(size - 2);
            boolean isArithmetic = true;
            for (int i = 1; i < size; i++) {
                if (sequence.get(i) - sequence.get(i - 1) != diff) {
                    isArithmetic = false;
                    break;
                }
            }
            if (isArithmetic) {
                return sequence.get(size - 1) + diff;
            }
        }

        // --- Check for Geometric Sequence (×N each step) ---
        if (size >= 2 && sequence.get(size - 2) != 0) {
            double ratio = (double) sequence.get(size - 1) / sequence.get(size - 2);
            boolean isGeometric = true;
            for (int i = 1; i < size; i++) {
                if (sequence.get(i - 1) == 0 ||
                        (double) sequence.get(i) / sequence.get(i - 1) != ratio) {
                    isGeometric = false;
                    break;
                }
            }
            if (isGeometric) {
                return (int) (sequence.get(size - 1) * ratio);
            }
        }

        // --- Check for Repeating Cycle ---
        for (int cycleLength = 1; cycleLength <= size / 2; cycleLength++) {
            boolean isRepeating = true;
            for (int i = 0; i < cycleLength; i++) {
                int pos = size - cycleLength + i;
                if (sequence.get(pos) != sequence.get(pos - cycleLength)) {
                    isRepeating = false;
                    break;
                }
            }
            if (isRepeating) {
                return sequence.get(size - cycleLength);
            }
        }

        // Fallback: Return the last observed value (or 1 for safety)
        return sequence.get(size - 1);
    }


    // Call this once at startup
    void initMarkovPredictor() {
        markovBuffer = new int[MARKOV_MEMORY_SIZE];
        int totalStates = (int) Math.pow(2, MARKOV_MEMORY_SIZE);
        for (int i = 0; i < totalStates; i++) {
            Map<Integer, Integer> state = new HashMap<>();
            state.put(0, 0);
            state.put(1, 0);
            markovTransitions.put(i, state);
        }
        if (SHOW_DEBUG_LOGS) System.out.println("Initialized predictor with memory=" + MARKOV_MEMORY_SIZE);
    }

    // Process a new bit (0 or 1)
    void processBit(int bit) {
        if (bit != 0 && bit != 1) throw new IllegalArgumentException("Bit must be 0 or 1");

        // Update global counts
        if (bit == 0) count0++; else count1++;

        // Update transitions if buffer is full
        if (totalBitsProcessed >= MARKOV_MEMORY_SIZE) {
            int currentState = getCurrentMarkovState();
            markovTransitions.get(currentState).merge(bit, 1, Integer::sum);
        }

        // Update circular buffer
        markovBuffer[markovBufferPointer] = bit;
        markovBufferPointer = (markovBufferPointer + 1) % MARKOV_MEMORY_SIZE;
        totalBitsProcessed++;

        if (SHOW_DEBUG_LOGS) {
            System.out.printf("Processed %d | State: %s | Transitions: %s%n",
                    bit,
                    Integer.toBinaryString(getCurrentMarkovState()),
                    markovTransitions.get(getCurrentMarkovState()));
        }
    }
    // Predict the next bit (0 or 1)
    int predictNextBit() {
        if (totalBitsProcessed < MARKOV_MEMORY_SIZE) {
            return count1 >= count0 ? 1 : 0;  // Fallback: majority vote
        }

        int currentState = getCurrentMarkovState();
        Map<Integer, Integer> transitions = markovTransitions.get(currentState);
        int transitions0 = transitions.get(0);
        int transitions1 = transitions.get(1);

        if (transitions0 == transitions1) {
            return count1 >= count0 ? 1 : 0;  // Tie-breaker
        }
        return transitions1 > transitions0 ? 1 : 0;
    }
    // Get prediction confidence (0.5 to 1.0)
    double getPredictionConfidence() {
        if (totalBitsProcessed <= MARKOV_MEMORY_SIZE) return 0.5;

        int currentState = getCurrentMarkovState();
        Map<Integer, Integer> transitions = markovTransitions.get(currentState);
        int total = transitions.get(0) + transitions.get(1);
        return total == 0 ? 0.5 : (double) Math.max(transitions.get(0), transitions.get(1)) / total;
    }
    // Helper: Get current state as integer
    private int getCurrentMarkovState() {
        int state = 0;
        for (int i = 0; i < MARKOV_MEMORY_SIZE; i++) {
            int pos = (markovBufferPointer + i) % MARKOV_MEMORY_SIZE;
            state = (state << 1) | markovBuffer[pos];
        }
        return state;
    }

    // Reset all state (keeps memory size)
    void resetMarkovPredictor() {
        markovTransitions.clear();
        markovBuffer = new int[MARKOV_MEMORY_SIZE];
        markovBufferPointer = 0;
        totalBitsProcessed = 0;
        count0 = 0;
        count1 = 0;
        initMarkovPredictor();
    }

}
