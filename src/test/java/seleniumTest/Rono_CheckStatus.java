package seleniumTest;

public class Rono_CheckStatus extends Rono_Names {

    public void checkStatus() {


//        if (betone_performed == 1) {
//            String resultString = currentresult;
//            if (resultString.equals(betone_string)) {
//                betone_string_betamount = restbetAmount;
//
//            } else {
//                betone_string_betamount = betone_string_betamount * 2;
//
//            }
//
//            betone_performed = 0;
//        }
//


        if (killBill_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(killBill_string)) {
                killBill_amount = restbetAmount;
                killBill_samecount = killBill_samecount + 1;
                killBill_diffcount = 0;
            } else {
                killBill_amount = killBill_amount * 2;
                killBill_diffcount = killBill_diffcount + 1;
                killBill_samecount = 0;
            }

            killBill_performed = 0;
        }
        if (reservoirDogs_performed == 1) {
            String resultString = currentresult;
//            sendMessageObuli("check status = > "+reservoirDogs_string);

            if (resultString.equals(reservoirDogs_string)) {

                reservoirDogs_amount = restbetAmount;
                reservoirDogs_samecount = reservoirDogs_samecount + 1;
                reservoirDogs_diffcount = 0;

            } else {
                reservoirDogs_amount = reservoirDogs_amount * 2;
                reservoirDogs_diffcount = reservoirDogs_diffcount + 1;
                reservoirDogs_samecount = 0;
            }

            reservoirDogs_performed = 0;
        }
        if (hatefulEight_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hatefulEight_string)) {
                hatefulEight_amount = restbetAmount;
                hatefulEight_samecount = hatefulEight_samecount + 1;
                hatefulEight_diffcount = 0;

            } else {
                hatefulEight_amount = hatefulEight_amount * 2;
                hatefulEight_diffcount = hatefulEight_diffcount + 1;
                hatefulEight_samecount = 0;

            }

            hatefulEight_performed = 0;
        }
        if (django_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(django_string)) {
                django_amount = restbetAmount;
                django_samecount =  django_samecount + 1;
                django_diffcount = 0;



            } else {
                django_amount = django_amount * 2;
                django_diffcount =  django_diffcount + 1;
                django_samecount = 0;
            }

            django_performed = 0;
        }
        if (pulpFiction_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(pulpFiction_string)) {
                pulpFiction_amount = restbetAmount;
                pulpFiction_samecount = pulpFiction_samecount + 1;
                pulpFiction_diffcount = 0;

            } else {
                pulpFiction_amount = pulpFiction_amount * 2;
                pulpFiction_diffcount = pulpFiction_diffcount + 1;
                pulpFiction_samecount = 0;

            }

            pulpFiction_performed = 0;
        }
        if (godfather_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(godfather_string)) {
                godfather_amount = restbetAmount;
                godfather_samecount = godfather_samecount + 1;
                godfather_diffcount = 0;

            } else {
                godfather_amount = godfather_amount * 2;
                godfather_diffcount = godfather_diffcount + 1;
                godfather_samecount = 0;

            }

            godfather_performed = 0;
        }
        if (goodfellas_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(goodfellas_string)) {
                goodfellas_amount = restbetAmount;

            } else {
                goodfellas_amount = goodfellas_amount * 2;

            }

            goodfellas_performed = 0;
        }
        if (whiplash_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(whiplash_string)) {
                whiplash_amount = restbetAmount;
                whiplash_samecount = whiplash_samecount + 1;
                whiplash_diffcount = 0;

            } else {
                whiplash_amount = whiplash_amount * 2;
                whiplash_diffcount = whiplash_diffcount + 1;
                whiplash_samecount = 0;

            }

            whiplash_performed = 0;
        }
        if (laLaLand_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(laLaLand_string)) {
                laLaLand_amount = restbetAmount;
                laLaLand_samecount = laLaLand_samecount + 1;
                laLaLand_diffcount = 0;

            } else {
                laLaLand_amount = laLaLand_amount * 2;
                laLaLand_diffcount = laLaLand_diffcount + 1;
                laLaLand_samecount = 0;

            }

            laLaLand_performed = 0;
        }


        if (parasite_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(parasite_string)) {
                parasite_amount = restbetAmount;
                parasite_samecount = parasite_samecount + 1;
                parasite_diffcount = 0;
            } else {
                parasite_amount = parasite_amount * 2;
                parasite_diffcount = parasite_diffcount + 1;
                parasite_samecount = 0;
            }

            parasite_performed = 0;
        }

        if (dunkirk_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(dunkirk_string)) {
                dunkirk_amount = restbetAmount;

            } else {
                dunkirk_amount = dunkirk_amount * 2;

            }

            dunkirk_performed = 0;
        }
        if (tenet_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(tenet_string)) {
                tenet_amount = restbetAmount;
                tenet_samecount = tenet_samecount + 1;
                tenet_diffcount = 0;
            } else {
                tenet_amount = tenet_amount * 2;
                tenet_diffcount = tenet_diffcount + 1;
                tenet_samecount = 0;
            }

            tenet_performed = 0;
        }


        if (dune_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(dune_string)) {
                dune_amount = restbetAmount;

            } else {
                dune_amount = dune_amount * 2;

            }

            dune_performed = 0;
        }


        if (lordRings_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(lordRings_string)) {
                lordRings_amount = restbetAmount;

            } else {
                lordRings_amount = lordRings_amount * 2;

            }

            lordRings_performed = 0;
        }


        if (hobbit_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hobbit_string)) {
                hobbit_amount = restbetAmount;
                hobbit_samecount = hobbit_samecount + 1;
                hobbit_diffcount = 0;
            } else {
                hobbit_amount = hobbit_amount * 2;
                hobbit_diffcount = hobbit_diffcount + 1;
                hobbit_samecount = 0;
            }

            hobbit_performed = 0;
        }

        if (elon_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(elon_string)) {
                elon_amount = restbetAmount;

            } else {
                elon_amount = elon_amount * 2;

            }

            elon_performed = 0;
        }

        if (steveJobs_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(steveJobs_string)) {
                steveJobs_amount = restbetAmount;

            } else {
                steveJobs_amount = steveJobs_amount * 2;

            }

            steveJobs_performed = 0;
        }

        if (tonystrak_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(tonystrak_string)) {
                tonystrak_amount = restbetAmount;

            } else {
                tonystrak_amount = tonystrak_amount * 2;

            }

            tonystrak_performed = 0;
        }


        if (harshini_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(harshini_string)) {
                harshini_amount = restbetAmount;
                harshini_samecount = harshini_samecount + 1;
                harshini_diffcount = 0;
                if (total > (index + 1)) {
                    index++;
                } else {
                    index = 0;
                }


            } else {
                harshini_amount = harshini_amount * 2;
                harshini_diffcount = harshini_diffcount + 1;
                harshini_samecount = 0;
                index = 0;


            }

            harshini_performed = 0;

        }

        if (divi3_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(divi3_string)) {
                divi3_amount = restbetAmount;
            } else {
                divi3_amount = divi3_amount * 2;

            }

            divi3_performed = 0;

        }
        if (justiceLeague_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(justiceLeague_string)) {
                justiceLeague_amount = restbetAmount;
                justiceLeague_samecount = justiceLeague_samecount + 1;
                justiceLeague_diffcount = 0;

            } else {
                justiceLeague_amount = justiceLeague_amount * 2;
                justiceLeague_diffcount = justiceLeague_diffcount + 1;
                justiceLeague_samecount = 0;

            }

            justiceLeague_performed = 0;
        }

        if (same1Perfromed == 1) {
            String resultString = currentresult;
            if (resultString.equals(same_string)) {
                same_string_betamount = restbetAmount;
                same_string_samecount = same_string_samecount + 1;
                same_string_diffcount = 0;

            } else {
                same_string_betamount = same_string_betamount * 2;
                same_string_diffcount = same_string_diffcount + 1;
                same_string_samecount = 0;

            }
            same1Perfromed = 0;

        }

        if (wonderWoman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(wonderWoman_string)) {
                wonderWoman_amount = restbetAmount;
                wonderWoman_samecount = wonderWoman_samecount + 1;
                wonderWoman_diffcount = 0;

            } else {
                wonderWoman_amount = wonderWoman_amount * 2;
                wonderWoman_diffcount = wonderWoman_diffcount + 1;
                wonderWoman_samecount = 0;

            }

            wonderWoman_performed = 0;
        }
        if (aquaman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(aquaman_string)) {
                aquaman_amount = restbetAmount;
                aquaman_samecount = aquaman_samecount + 1;
                aquaman_diffcount = 0;

            } else {
                aquaman_amount = aquaman_amount * 2;
                aquaman_diffcount = aquaman_diffcount + 1;
                aquaman_samecount = 0;

            }

            aquaman_performed = 0;
        }
        if (superman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(superman_string)) {
                superman_amount = restbetAmount;
                superman_samecount = superman_samecount + 1;
                superman_diffcount = 0;

            } else {
                superman_amount = superman_amount * 2;
                superman_diffcount = superman_diffcount + 1;
                superman_samecount = 0;

            }

            superman_performed = 0;
        }
        if (batman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(batman_string)) {
                batman_amount = restbetAmount;
                batman_samecount = batman_samecount + 1;
                batman_diffcount = 0;
            } else {
                batman_amount = batman_amount * 2;
                batman_diffcount = batman_diffcount+1;
                batman_samecount=0;

            }

            batman_performed = 0;
        }
        if (spiderman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(spiderman_string)) {
                spiderman_amount = restbetAmount;
                spiderman_samecount = spiderman_samecount + 1;
                spiderman_diffcount = 0;

            } else {
                spiderman_amount = spiderman_amount * 2;
                spiderman_diffcount = spiderman_diffcount + 1;
                spiderman_samecount = 0;

            }

            spiderman_performed = 0;
        }
        if (megamind_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(megamind_string)) {
                megamind_amount = restbetAmount;
                megamind_samecount = megamind_samecount + 1;
                megamind_diffcount = 0;

            } else {
                megamind_amount = megamind_amount * 2;
                megamind_diffcount = megamind_diffcount + 1;
                megamind_samecount = 0;

            }

            megamind_performed = 0;
        }
        if (hotelTransylvania_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hotelTransylvania_string)) {
                hotelTransylvania_amount = restbetAmount;
                hotelTransylvania_samecount = hotelTransylvania_samecount + 1;
                hotelTransylvania_diffcount = 0;

            } else {
                hotelTransylvania_amount = hotelTransylvania_amount * 2;
                hotelTransylvania_diffcount = hotelTransylvania_diffcount + 1;
                hotelTransylvania_samecount = 0;

            }

            hotelTransylvania_performed = 0;
        }
        if (smurfs_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(smurfs_string)) {
                smurfs_amount = restbetAmount;
                smurfs_samecount = smurfs_samecount + 1;
                smurfs_diffcount = 0;

            } else {
                smurfs_amount = smurfs_amount * 2;
                smurfs_diffcount = smurfs_diffcount + 1;
                smurfs_samecount = 0;

            }

            smurfs_performed = 0;
        }
        if (emojiMovie_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(emojiMovie_string)) {
                emojiMovie_amount = restbetAmount;
                emojiMovie_samecount = emojiMovie_samecount + 1;
                emojiMovie_diffcount = 0;

            } else {
                emojiMovie_amount = emojiMovie_amount * 2;
                emojiMovie_diffcount = emojiMovie_diffcount + 1;
                emojiMovie_samecount = 0;

            }

            emojiMovie_performed = 0;
        }
        if (cloudyMeatballs_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(cloudyMeatballs_string)) {
                cloudyMeatballs_amount = restbetAmount;
                cloudyMeatballs_samecount = cloudyMeatballs_samecount + 1;
                cloudyMeatballs_diffcount = 0;

            } else {
                cloudyMeatballs_amount = cloudyMeatballs_amount * 2;
                cloudyMeatballs_diffcount = cloudyMeatballs_diffcount + 1;
                cloudyMeatballs_samecount = 0;

            }

            cloudyMeatballs_performed = 0;
        }

        if (iceAge_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(iceAge_string)) {
                iceAge_amount = restbetAmount;
                iceAge_samecount = iceAge_samecount + 1;
                iceAge_diffcount = 0;

            } else {
                iceAge_amount = iceAge_amount * 2;
                iceAge_diffcount = iceAge_diffcount + 1;
                iceAge_samecount = 0;

            }

            iceAge_performed = 0;
        }
        if (despicableMe_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(despicableMe_string)) {
                despicableMe_amount = restbetAmount;
                despicableMe_samecount = despicableMe_samecount + 1;
                despicableMe_diffcount = 0;

            } else {
                despicableMe_amount = despicableMe_amount * 2;
                despicableMe_diffcount = despicableMe_diffcount + 1;
                despicableMe_samecount = 0;

            }

            despicableMe_performed = 0;
        }
        if (madagascar_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(madagascar_string)) {
                madagascar_amount = restbetAmount;
                madagascar_samecount = madagascar_samecount + 1;
                madagascar_diffcount = 0;

            } else {
                madagascar_amount = madagascar_amount * 2;
                madagascar_diffcount = madagascar_diffcount + 1;
                madagascar_samecount = 0;

            }

            madagascar_performed = 0;
        }
        if (minions_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(minions_string)) {
                minions_amount = restbetAmount;
                minions_samecount = minions_samecount + 1;
                minions_diffcount = 0;

            } else {
                minions_amount = minions_amount * 2;
                minions_diffcount = minions_diffcount + 1;
                minions_samecount = 0;

            }

            minions_performed = 0;
        }

        if (sing_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(sing_string)) {
                sing_amount = restbetAmount;
                sing_samecount = sing_samecount + 1;
                sing_diffcount = 0;

            } else {
                sing_amount = sing_amount * 2;
                sing_diffcount = sing_diffcount + 1;
                sing_samecount = 0;

            }

            sing_performed = 0;
        }

        if (frozen2_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(frozen2_string)) {
                frozen2_amount = restbetAmount;
                frozen2_samecount = frozen2_samecount + 1;
                frozen2_diffcount = 0;

            } else {
                frozen2_amount = frozen2_amount * 2;
                frozen2_diffcount = frozen2_diffcount + 1;
                frozen2_samecount = 0;

            }

            frozen2_performed = 0;
        }
        if (lionKing_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(lionKing_string)) {
                lionKing_amount = restbetAmount;
                lionKing_samecount = lionKing_samecount + 1;
                lionKing_diffcount = 0;

            } else {
                lionKing_amount = lionKing_amount * 2;
                lionKing_diffcount = lionKing_diffcount + 1;
                lionKing_samecount = 0;

            }

            lionKing_performed = 0;
        }
        if (beautyAndBeast_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(beautyAndBeast_string)) {
                beautyAndBeast_amount = restbetAmount;
                beautyAndBeast_samecount = beautyAndBeast_samecount + 1;
                beautyAndBeast_diffcount = 0;

            } else {
                beautyAndBeast_amount = beautyAndBeast_amount * 2;
                beautyAndBeast_diffcount = beautyAndBeast_diffcount + 1;
                beautyAndBeast_samecount = 0;

            }

            beautyAndBeast_performed = 0;
        }
        if (hercules_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hercules_string)) {
                hercules_amount = restbetAmount;
                hercules_samecount = hercules_samecount + 1;
                hercules_diffcount = 0;

            } else {
                hercules_amount = hercules_amount * 2;
                hercules_diffcount = hercules_diffcount + 1;
                hercules_samecount = 0;

            }

            hercules_performed = 0;
        }

        if (mulan_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(mulan_string)) {
                mulan_amount = restbetAmount;
                mulan_samecount = mulan_samecount + 1;
                mulan_diffcount = 0;

            } else {
                mulan_amount = mulan_amount * 2;
                mulan_diffcount = mulan_diffcount + 1;
                mulan_samecount = 0;
            }

            mulan_performed = 0;
        }


        if (aladdin_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(aladdin_string)) {
                aladdin_amount = restbetAmount;
                aladdin_samecount = aladdin_samecount + 1;
                aladdin_diffcount = 0;

            } else {
                aladdin_amount = aladdin_amount * 2;
                aladdin_diffcount = aladdin_diffcount + 1;
                aladdin_samecount = 0;

            }

            aladdin_performed = 0;
        }
        if (encanto_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(encanto_string)) {
                encanto_amount = restbetAmount;
                encanto_samecount = encanto_samecount + 1;
                encanto_diffcount = 0;

            } else {
                encanto_amount = encanto_amount * 2;
                encanto_diffcount = encanto_diffcount + 1;
                encanto_samecount = 0;

            }

            encanto_performed = 0;
        }
        if (soul_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(soul_string)) {
                soul_amount = restbetAmount;
                soul_samecount = soul_samecount + 1;
                soul_diffcount = 0;

            } else {
                soul_amount = soul_amount * 2;
                soul_diffcount = soul_diffcount + 1;
                soul_samecount = 0;

            }

            soul_performed = 0;
        }
        if (findingNemo_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(findingNemo_string)) {
                findingNemo_amount = restbetAmount;
                findingNemo_samecount = findingNemo_samecount + 1;
                findingNemo_diffcount = 0;

            } else {
                findingNemo_amount = findingNemo_amount * 2;
                findingNemo_diffcount = findingNemo_diffcount + 1;
                findingNemo_samecount = 0;

            }

            findingNemo_performed = 0;
        }
        if (wallE_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(wallE_string)) {
                wallE_amount = restbetAmount;

            } else {
                wallE_amount = wallE_amount * 2;

            }

            wallE_performed = 0;
        }
        if (bigHero6_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(bigHero6_string)) {
                bigHero6_amount = restbetAmount;
                bigHero6_samecount = bigHero6_samecount + 1;
                bigHero6_diffcount = 0;

            } else {
                bigHero6_diffcount = bigHero6_diffcount + 1;
                bigHero6_samecount = 0;
                bigHero6_amount = bigHero6_amount * 2;

            }

            bigHero6_performed = 0;
        }
        if (zootopia_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(zootopia_string)) {
                zootopia_amount = restbetAmount;
                zootopia_samecount = zootopia_samecount + 1;
                zootopia_diffcount = 0;
                if (total > (index + 1)) {
                    index += 1;
                } else {
                    index = 0;
                }
            } else {
                zootopia_amount = zootopia_amount * 2;
                zootopia_diffcount = zootopia_diffcount + 1;
                zootopia_samecount = 0;

                if (total > (index + 1)) {
                    index += 1;
                } else {
                    index = 0;
                }
            }

            zootopia_performed = 0;
        }
        if (kungFuPanda_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(kungFuPanda_string)) {
                kungFuPanda_amount = restbetAmount;
                kungFuPanda_samecount = kungFuPanda_samecount + 1;
                kungFuPanda_diffcount = 0;

            } else {
                kungFuPanda_amount = kungFuPanda_amount * 2;
                kungFuPanda_diffcount = kungFuPanda_diffcount + 1;
                kungFuPanda_samecount = 0;
            }

            kungFuPanda_performed = 0;
        }
        if (shrek_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(shrek_string)) {
                shrek_amount = restbetAmount;
                shrek_samecount = shrek_samecount + 1;
                shrek_diffcount = 0;

            } else {
                shrek_amount = shrek_amount * 2;
                shrek_diffcount = shrek_diffcount + 1;
                shrek_samecount = 0;
            }

            shrek_performed = 0;
        }
        if (brave_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(brave_string)) {
                brave_amount = restbetAmount;
                brave_samecount = brave_samecount + 1;
                brave_diffcount = 0;

            } else {
                brave_amount = brave_amount * 2;
                brave_diffcount = brave_diffcount + 1;
                brave_samecount = 0;
            }

            brave_performed = 0;
        }
        if (toyStory_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(toyStory_string)) {
                toyStory_amount = restbetAmount;
                toyStory_samecount = toyStory_samecount + 1;
                toyStory_diffcount = 0;

            } else {
                toyStory_amount = toyStory_amount * 2;
                toyStory_diffcount = toyStory_diffcount + 1;
                toyStory_samecount = 0;
            }

            toyStory_performed = 0;
        }
        if (ratatouille_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(ratatouille_string)) {
                ratatouille_amount = restbetAmount;
                ratatouille_samecount = ratatouille_samecount + 1;
                ratatouille_diffcount = 0;
            } else {
                ratatouille_amount = ratatouille_amount * 2;
                ratatouille_diffcount = ratatouille_diffcount + 1;
                ratatouille_samecount = 0;
            }

            ratatouille_performed = 0;
        }

        if (coco_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(coco_string)) {
                coco_samecount = coco_samecount + 1;
                coco_diffcount = 0;
                coco_amount = restbetAmount;

            } else {
                coco_amount = coco_amount * 2;
                coco_diffcount = coco_diffcount + 1;
                coco_samecount = 0;
            }

            coco_performed = 0;
        }

        if (cars_performed == 1) {
            String resultString = currentresult;

            if (resultString.equals(cars_string)) {
                cars_same = cars_same + 1;
                cars_diff = 0;
                cars_amount = restbetAmount;
            } else {
                cars_amount = cars_amount * 2;
                cars_diff = cars_diff + 1;
                cars_same = 0;
            }

            cars_performed = 0;
        }


        if (moana_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(moana_string)) {
                moana_amount = restbetAmount;
                moana_samecount = moana_samecount + 1;
                moana_diffcount = 0;

            } else {
                moana_amount = moana_amount * 2;
                moana_diffcount = moana_diffcount + 1;
                moana_samecount = 0;
            }
            moana_performed = 0;
        }
        if (up_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(up_string)) {
                up_amount = restbetAmount;
            } else {
                up_amount = up_amount * 2;
            }
            up_performed = 0;
        }
        if (tangled_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(tangled_string)) {
                tangled_amount = restbetAmount;
                tangled_samecount = tangled_samecount + 1;
                tangled_diffcount = 0;
            } else {
                tangled_amount = tangled_amount * 2;
                tangled_diffcount = tangled_diffcount + 1;
                tangled_samecount = 0;
            }
            tangled_performed = 0;
        }
        if (frozen_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(frozen_string)) {
                frozen_amount = restbetAmount;
                frozen_samecount = frozen_samecount + 1;
                frozen_diffcount = 0;


            } else {
                frozen_amount = frozen_amount * 2;
                frozen_diffcount = frozen_diffcount + 1;
                frozen_samecount = 0;

            }
            frozen_performed = 0;
        }
        if (inception2_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(inception2_string)) {
                inception2_amount = restbetAmount;
                inception2_samecount = inception2_samecount + 1;
                inception2_diffcount = 0;
            } else {
                inception2_amount = inception2_amount * 2;
                inception2_diffcount = inception2_diffcount + 1;
                inception2_samecount = 0;
            }
            inception2_performed = 0;
        }
        if (endgame_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(endgame_string)) {
                endgame_amount = restbetAmount;
                endgame_samecount = endgame_samecount + 1;
                endgame_diffcount = 0;
            } else {
                endgame_amount = endgame_amount * 2;
                endgame_diffcount = endgame_diffcount + 1;
                endgame_samecount = 0;
            }
            endgame_performed = 0;
        }


        if (blackPanther_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(blackPanther_string)) {
                blackPanther_amount = restbetAmount;
                blackPanther_samecount = blackPanther_samecount + 1;
                blackPanther_diffcount = 0;

            } else {
                blackPanther_amount = blackPanther_amount * 2;
                blackPanther_diffcount = blackPanther_diffcount + 1;
                blackPanther_samecount = 0;
            }
            blackPanther_performed = 0;
        }
        if (ironMan_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(ironMan_string)) {
                ironMan_amount = restbetAmount;
                ironMan_samecount = ironMan_samecount + 1;
                ironMan_diffcount = 0;
            } else {
                ironMan_amount = ironMan_amount * 2;
                ironMan_diffcount = ironMan_diffcount + 1;
                ironMan_samecount = 0;
            }
            ironMan_performed = 0;
        }

        if (thor_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(thor_string)) {
                thor_amount = restbetAmount;
                thor_samecount = thor_samecount + 1;
                thor_diffcount = 0;



            } else {
                thor_amount = thor_amount * 2;
                thor_diffcount = thor_diffcount + 1;
                thor_samecount = 0;

            }

            thor_performed = 0;
        }

        if (avengers_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(avengers_string)) {
                avengers_amount = restbetAmount;
                avengers_samecount = avengers_samecount + 1;
                avengers_diffcount = 0;


            } else {
                avengers_amount = avengers_amount * 2;
                avengers_diffcount = avengers_diffcount + 1;
                avengers_samecount = 0;

            }

            avengers_performed = 0;
        }

        if (matrix_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(matrix_string)) {
                matrix_amount = restbetAmount;
                matrix_samecount = matrix_samecount + 1;
                matrix_diffcount = 0;

            } else {
                matrix_amount = matrix_amount * 2;
                matrix_diffcount = matrix_diffcount + 1;
                matrix_samecount = 0;

            }

            matrix_performed = 0;
        }

        if (interstellar_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(interstellar_string)) {
                interstellar_amount = restbetAmount;
                interstellar_samecount = interstellar_samecount + 1;
                interstellar_diffcount = 0;

            } else {
                interstellar_amount = interstellar_amount * 2;
                interstellar_diffcount = interstellar_diffcount + 1;
                interstellar_samecount = 0;

            }

            interstellar_performed = 0;
        }

        if (gladiator_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(gladiator_string)) {
                gladiator_amount = restbetAmount;
                gladiator_samecount = gladiator_samecount + 1;
                gladiator_diffcount = 0;

            } else {
                gladiator_amount = gladiator_amount * 2;
                gladiator_diffcount = gladiator_diffcount + 1;
                gladiator_samecount = 0;

            }

            gladiator_performed = 0;
        }

        if (joker_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(joker_string)) {
                joker_amount = restbetAmount;
                joker_samecount = joker_samecount + 1;
                joker_diffcount = 0;

            } else {
                joker_amount = joker_amount * 2;
                joker_diffcount = joker_diffcount + 1;
                joker_samecount = 0;

            }

            joker_performed = 0;
        }

        if (titanic_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(titanic_string)) {
                titanic_amount = restbetAmount;
                titanic_samecount = titanic_samecount + 1;
                titanic_diffcount = 0;

            } else {
                titanic_amount = titanic_amount * 2;
                titanic_diffcount = titanic_diffcount + 1;
                titanic_samecount = 0;

            }

            titanic_performed = 0;
        }
        if (avatar_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(avatar_string)) {
                avatar_amount = restbetAmount;
                avatar_samecount = avatar_samecount + 1;
                avatar_diffcount = 0;

            } else {
                avatar_amount = avatar_amount * 2;
                avatar_diffcount = avatar_diffcount + 1;
                avatar_samecount = 0;

            }

            avatar_performed = 0;
        }

        if (inception_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(inception_string)) {
                inception_amount = restbetAmount;
                inception_samecount = inception_samecount + 1;
                inception_diffcount = 0;

            } else {
                inception_amount = inception_amount * 2;
                inception_diffcount = inception_diffcount + 1;
                inception_samecount = 0;

            }

            inception_performed = 0;
        }

    }
}
