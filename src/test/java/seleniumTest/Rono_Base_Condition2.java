package seleniumTest;

public class Rono_Base_Condition2 extends Rono_Base_Condition1 {

    public void layer2() throws InterruptedException {
        what_string = tenet_string;

        if (tenet_samecount >= 1 && tenet_samecount < 3) {
            cars_string = what_string;
            cars_performed = 1;
            sendMessagealbin("current => " + cars_string + " => " + cars_amount);


            if (cars_amount == 1 || cars_amount == 2 || cars_amount == 4) {
                brave_string = what_string;
                brave_performed = 1;
                sendMessage16("current => " + brave_string + " => " + brave_amount);

                if (brave_amount == 1) {
                    shrek_string = opposite_string(brave_string);
                    shrek_performed = 1;
                    sendMessage32("current => " + shrek_string + " => " + shrek_amount);

                } else {
                    shrek_string = what_string;
                    shrek_performed = 1;
                    sendMessage32("current => " + shrek_string + " => " + shrek_amount);

                }

                brave_amount();

            }

            cars_amount();
        }


        if (tenet_amount == 2) {
            coco_string = what_string;
            coco_performed = 1;
            sendMessage2("current => " + coco_string + " => " + coco_amount);
            coco_amount();


        }
        if (tenet_amount == 4) {
            ratatouille_string = what_string;
            ratatouille_performed = 1;

            sendMessage4("current => " + ratatouille_string + " => " + ratatouille_amount);
            ratatouille_amount();

        }
        if (tenet_amount == 8) {
            toyStory_string = what_string;
            toyStory_performed = 1;
            sendMessage8("current => " + toyStory_string + " => " + toyStory_amount);
            toyStory_amount();

        }



    }

    private void cars_amount() throws InterruptedException {

        if (cars_amount == 1) {
            bigHero6_string = cars_string;
            bigHero6_performed = 1;
            sendMessageHar1("current => " + bigHero6_string + " => " + bigHero6_amount);

//            on_the_flow(bigHero6_string, bigHero6_amount);
//            layers2();


        }
        if (cars_amount == 2) {
            wallE_string = (what_string);
            wallE_performed = 1;

            sendMessageHar2("current => " + wallE_string + " => " + wallE_amount);
//            on_the_flow((wallE_string), wallE_amount);
        }

        if (cars_amount == 4) {
            findingNemo_string = cars_string;
            findingNemo_performed = 1;


            if (Balance_diff > 2000) {
                godfather_string = "S";
                godfather_performed = 1;
//                sendMessageSuresh("D6 => " + godfather_string + " => " + godfather_amount);
//                performBet("S", godfather_amount * 1000);
            }

//            int go = findPreviousValue1(Balance);
//
//            performBet(opposite_string(what_string),go);
//            sendMessageHarshini("Prev => " + previous_Balance + " => Cur => " + Balance );

            sendMessageHar4("current => " + findingNemo_string + " => " + findingNemo_amount);


        }
        if (cars_amount == 8) {
            soul_string = cars_string;
            soul_performed = 1;
            sendMessageHar8("current => " + soul_string + " => " + soul_amount);
        }
        if (cars_amount == 16) {
            encanto_string = cars_string;
            encanto_performed = 1;
            sendMessageHar16("current => " + encanto_string + " => " + encanto_amount);
        }
        if (cars_amount == 32) {
            aladdin_string = cars_string;
            aladdin_performed = 1;
            sendMessageobuli32("current => " + aladdin_string + " => " + aladdin_amount);
        }


    }

    private void coco_amount() throws InterruptedException {
        if (coco_amount == 1) {
            mulan_string = (what_string);
            mulan_performed = 1;
            sendMessageKis1("current => " + mulan_string + " => " + mulan_amount);
        }

        if (coco_amount == 2) {
            hercules_string = what_string;
            hercules_performed = 1;
            sendMessageKis2("current => " + hercules_string + " => " + hercules_amount);
        }
        if (coco_amount == 4) {
            beautyAndBeast_string = what_string;
            beautyAndBeast_performed = 1;
            sendMessageKis4("current => " + beautyAndBeast_string + " => " + beautyAndBeast_amount);
        }

        if (coco_amount == 8) {
            lionKing_string = what_string;
            lionKing_performed = 1;
            sendMessageKis8("current => " + lionKing_string + " => " + lionKing_amount);
        }

        if (coco_amount == 16) {
            frozen2_string = what_string;
            frozen2_performed = 1;
            sendMessageKis16("current => " + frozen2_string + " => " + frozen2_amount);
        }


    }

    private void ratatouille_amount() throws InterruptedException {
        if (ratatouille_amount == 1) {
            sing_string = what_string;
            sing_performed = 1;
            sendLast2("current => " + sing_string + " => " + sing_amount);
        }

        if (ratatouille_amount == 2) {
            minions_string = what_string;
            minions_performed = 1;

            sendLast4("current => " + minions_string + " => " + minions_amount);
        }
        if (ratatouille_amount == 4) {
            madagascar_string = what_string;
            madagascar_performed = 1;
            sendLast8("current => " + madagascar_string + " => " + madagascar_amount);
        }
        if (ratatouille_amount == 8) {
            despicableMe_string = what_string;
            despicableMe_performed = 1;
            sendLast16("current => " + despicableMe_string + " => " + despicableMe_amount);
        }
        if (ratatouille_amount == 16) {
            iceAge_string = what_string;
            iceAge_performed = 1;
            sendLast32("current => " + iceAge_string + " => " + iceAge_amount);
        }


    }

    private void toyStory_amount() {
        if (toyStory_amount == 1) {
            cloudyMeatballs_string = what_string;
            cloudyMeatballs_performed = 1;
            sendMessageobuli1("current => " + cloudyMeatballs_string + " => " + cloudyMeatballs_amount);
        }
        if (toyStory_amount == 2) {
            emojiMovie_string = what_string;
            emojiMovie_performed = 1;
            sendMessageobuli2("current => " + emojiMovie_string + " => " + emojiMovie_amount);
        }
        if (toyStory_amount == 4) {
            smurfs_string = what_string;
            smurfs_performed = 1;
            sendMessageobuli4("current => " + smurfs_string + " => " + smurfs_amount);
        }
        if (toyStory_amount == 8) {
            hotelTransylvania_string = what_string;
            hotelTransylvania_performed = 1;
            sendMessageobuli8("current => " + hotelTransylvania_string + " => " + hotelTransylvania_amount);
        }
        if (toyStory_amount == 16) {
            megamind_string = what_string;
            megamind_performed = 1;
            sendMessageobuli16("current => " + megamind_string + " => " + megamind_amount);

        }
        if (toyStory_amount == 32) {
            spiderman_string = what_string;
            spiderman_performed = 1;
        }


    }

    private void brave_amount() throws InterruptedException {
        if (shrek_amount == 1) {
            batman_string = shrek_string;
            batman_performed = 1;
            sendMessagePadma1("current => " + batman_string + " => " + batman_amount);

/*
            if (batman_amount < 128) {
                sendMessageHarshini("Prev => " + previous_Balance + " => Cur => " + Balance);
                Thread.sleep(5000);
                performBet(opposite_string(batman_string), batman_amount);
            }
*/

            layers2();
        }
        if (shrek_amount == 2) {
            superman_string = shrek_string;
            superman_performed = 1;

            sendMessagePadma2("current => " + superman_string + " => " + superman_amount);

        }
        if (shrek_amount == 4) {
            aquaman_string = shrek_string;
            aquaman_performed = 1;
            sendMessagePadma4("current => " + aquaman_string + " => " + aquaman_amount);
        }
        if (shrek_amount == 8) {
            wonderWoman_string = shrek_string;
            wonderWoman_performed = 1;
            sendMessagePadma8("current => " + wonderWoman_string + " => " + wonderWoman_amount);
        }
    }


    void layers1() throws InterruptedException {

        if (batman_amount == 1) {
            dunkirk_string = what_string;
            dunkirk_performed = 1;
            sendMessageDivi1("D1 => " + dunkirk_string + " => " + dunkirk_amount);
//            on_the_flow(dunkirk_string, dunkirk_amount);


            if (dunkirk_amount == 1) {
                parasite_string = what_string;
                parasite_performed = 1;
                sendMessageDivi2("D2 => " + parasite_string + " => " + parasite_amount);
                if (parasite_samecount == 1) {
                    hatefulEight_string = what_string;
                    hatefulEight_performed = 1;
                    sendMessageDivi8("D9 => " + hatefulEight_string + " => " + hatefulEight_amount);

                }

                if (parasite_amount == 1) {
                    laLaLand_string = what_string;
                    laLaLand_performed = 1;
                    sendMessageDivi3("D3 => " + laLaLand_string + " => " + laLaLand_amount);
                    if (laLaLand_amount == 1) {
                        whiplash_string = what_string;
                        whiplash_performed = 1;
                        sendMessageDivi4("D4 => " + whiplash_string + " => " + whiplash_amount);
                        if (whiplash_amount == 1) {
                            goodfellas_string = what_string;
                            goodfellas_performed = 1;
                            sendMessageDivi5("D5 => " + goodfellas_string + " => " + goodfellas_amount);
                            if (goodfellas_amount == 1) {
                                godfather_string = what_string;
                                godfather_performed = 1;
                                sendMessageDivi6("D6 => " + godfather_string + " => " + godfather_amount);

                            }
                        }
                    }

                }
            }

        }

    }

    void layers2() throws InterruptedException {
        if (batman_amount == 1) {
            killBill_string = shrek_string;
            killBill_performed = 1;
            sendMessageRamu1("R1 => " + killBill_string + " => " + killBill_amount);

//            if(killBill_amount < 128){
//                performBet(opposite_string(killBill_string), killBill_amount);
//            }

        }
        if (killBill_amount == 1) {
            reservoirDogs_string = (shrek_string);
            reservoirDogs_performed = 1;
            sendMessageRamu2("R2 => " + reservoirDogs_string + " => " + reservoirDogs_amount);
            if (reservoirDogs_amount == 1) {
                hobbit_string = shrek_string;
                hobbit_performed = 1;
                sendMessageRamu3("R3 => " + hobbit_string + " => " + hobbit_amount);
                if (hobbit_amount == 1) {
                    lordRings_string = shrek_string;
                    lordRings_performed = 1;
                    sendMessageRamu4("R2 => " + lordRings_string + " => " + lordRings_amount);
                }
            }
        }




     /*   if (bigHero6_amount == 1) {
            killBill_string = what_string;
            killBill_performed = 1;
            sendMessageRamu1("R1 => " + killBill_string + " => " + killBill_amount);
            if (killBill_amount == 1) {
                reservoirDogs_string = (what_string);
                reservoirDogs_performed = 1;
                sendMessageRamu2("R2 => " + reservoirDogs_string + " => " + reservoirDogs_amount);
                if (reservoirDogs_amount == 1) {
                    hobbit_string = reservoirDogs_string;
                    hobbit_performed = 1;
                    sendMessageRamu3("R3 => " + hobbit_string + " => " + hobbit_amount);
                    if (hobbit_amount == 1) {
                        lordRings_string = reservoirDogs_string;
                        lordRings_performed = 1;
                        sendMessageRamu4("R2 => " + lordRings_string + " => " + lordRings_amount);


                    }
                }
            }

        }*/

    }


    public void podu(int amount, String String) throws InterruptedException {
        Thread.sleep(9000);


        if (previous_Balance <= Balance /*|| pulpFiction_amount == 16*/) {
            previous_Balance = Balance;
        }


        if (Math.abs(previous_Balance - Balance) <= 1) {
            previous_Balance = Balance;
            sendMessageRamu6("------------------");
            sendMessageDivi10("Prev => " + previous_Balance + " => Cur => " + Balance + " => diff => " + Balance_diff);

            performBet(opposite_string(String), 5);
            sendMessageRamu6("Normal --- " + 5);
            new_betAmount7 = 0;
            increase_amount7 = 0;

        } else {
            sendMessageSuresh("------------------");

            sendMessageDivi10("PreI => " + previous_Balance + " => Cur => " + Balance + " => diff => " + Balance_diff);

            on_flow_value_check(amount, String);


        }

    }


    public void on_the_flow(String what_string, int amount) throws InterruptedException {

        int bet_amounts = 1;


        if (previous_Balance <= Balance /*|| pulpFiction_amount == 16*/) {
            previous_Balance = Balance;
        }


        sendMessageHarshini("Prev => " + previous_Balance + " => Cur => " + Balance);
//        sendMessageObuli("Diff => " + (Balance - inital_Balance));

        Thread.sleep(5000);

        if (amount < 512) {
            if (Math.abs(previous_Balance - Balance) <= 1) {
                sendMessageObuli("Performing =>  " + opposite_string(what_string) + "  => Amount =>  " + bet_amounts);
                performBet(opposite_string(what_string), bet_amounts);
            } else {
                performBet(opposite_string(what_string), amount * bet_amounts);
                sendMessageObuli("Performing =>  " + opposite_string(what_string) + "  => Amount =>  " + (amount * bet_amounts));
            }
        } else {
            performBet(opposite_string(what_string), bet_amounts);
            sendMessageObuli("Oerforming =>  " + opposite_string(what_string) + "  => Amount =>  " + (bet_amounts));
        }


    }
}