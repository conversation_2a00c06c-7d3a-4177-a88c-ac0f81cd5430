package seleniumTest;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.testng.annotations.Test;

import java.io.File;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RonoBase extends Rono_Base_Condition2 {

    @Test
    public void everymin() throws InterruptedException {

        driver.get(url);
        sendKeysToElement(phonenumberpath, phoneNumber);
        sendKeysToElement(passwordpath, password);
        clickbutton(loginbutton);
        Thread.sleep(6000);
        driver.get(wingo1);


        try {
            while (true) {
                try {
                    waitForInternet();
                    LocalTime now = LocalTime.now();
                    int currentMinute = now.getMinute();
                    int currentSecond = now.getSecond();

//                    if (minutesToPrint_3.contains(currentMinute) && (currentSecond >= 1 && currentSecond <= 5)) {
                    if ((currentSecond >= 1 && currentSecond <= 5)) {


                        driver.navigate().refresh();
                        Thread.sleep(8000);


                        getresult1();
                        checkStatus();
                        getBalance();


                        if (previous_Balance <= Balance) {
                            previous_Balance = Balance;
                        }


                        Balance_diff = previous_Balance - Balance;
                        max_Balance = Math.max(max_Balance, Balance_diff);

//                        sendMessageSuresh("Bal =>" + balanceValue + "  => Prev => " + previous_balanceValue);
//
//                        if (balanceValue >= previous_balanceValue) {
//                            sendMessageSuresh("Reset");
//                            harshini_amount = 1;
//                            tenet_amount = 1;
//                        }

                        if (currentresult.equals(previousresult)) {
                            MasterbetAmount = restbetAmount;
                            samecount = samecount + 1;
                            diffcount = 0;
                        } else {
                            MasterbetAmount *= 2;
                            diffcount = diffcount + 1;
                            samecount = 0;
                        }
//                        sendMessageKishore("current => " + currentresult + " => " + MasterbetAmount);

                        harshini_string = arr[index];
                        harshini_performed = 1;
//                        sendMessageDivi1("Current => " + harshini_string + " => " + harshini_amount );


                        tenet_string = opposite_string(harshini_string);
                        tenet_performed = 1;
                        sendMessageDivi2("current => " + tenet_string + " => " + tenet_amount);
                        layer3();

                        whiplash_string = (harshini_amount == 1) ? opposite_string(harshini_string) : harshini_string;
                        whiplash_performed = 1;
//                        sendMessageDivi3("D4 => " + whiplash_string + " => " + whiplash_amount);


                        dunkirk_string = (tenet_amount < 4) ? opposite_string(tenet_string) : tenet_string;
                        dunkirk_performed = 1;
//                        sendMessageDivi4("D1 => " + dunkirk_string + " => " + dunkirk_amount);

//



//
                    }


                    Thread.sleep(2000);
                } catch (Exception e) {
                    e.printStackTrace();
                    takeScreenshot();
                    sendMessage("Error occurred: " + e.getMessage());
                    continue;
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
            takeScreenshot();
            sendMessage("Critical error occurred: " + e.getMessage());
        }
    }


    public void takeScreenshot() {
        try {
            File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
            screenshot.renameTo(new File("error_screenshot_" + System.currentTimeMillis() + ".png"));
        } catch (Exception e) {
            System.out.println("Failed to take screenshot: " + e.getMessage());
        }
    }

}

class LZ78PredictorFunctions {
    // Static variables to maintain state between function calls
    private static Map<String, Integer[]> patternStats = new HashMap<>(); // [count0, count1]
    private static String currentContext = "";
    private static int correctPredictions = 0;
    private static int totalPredictions = 0;
    private static final int MAX_CONTEXT_LENGTH = 10;

    // Reset the predictor
    public static void resetPredictor() {
        patternStats.clear();
        currentContext = "";
        correctPredictions = 0;
        totalPredictions = 0;
    }

    // Process a new bit and return the prediction for the next bit
    public static int processBit(int bit) {
        // Make prediction before updating the model
        int prediction = predictNextBit();

        // Only count as prediction if we have some context
        if (!currentContext.isEmpty()) {
            if (prediction == bit) {
                correctPredictions++;
            }
            totalPredictions++;
        }

        // Update statistics for the current context
        if (!currentContext.isEmpty()) {
            Integer[] counts = patternStats.getOrDefault(currentContext, new Integer[]{0, 0});
            counts[bit]++;
            patternStats.put(currentContext, counts);
        }

        // Update the current context (sliding window)
        currentContext += bit;
        if (currentContext.length() > MAX_CONTEXT_LENGTH) {
            currentContext = currentContext.substring(currentContext.length() - MAX_CONTEXT_LENGTH);
        }

        return prediction;
    }

    // Predict the next bit based on current context
    private static int predictNextBit() {
        if (currentContext.isEmpty()) {
            return 0; // Default prediction when no context
        }

        Integer[] counts = patternStats.get(currentContext);
        if (counts == null) {
            // Try shorter contexts if exact match not found
            for (int len = currentContext.length() - 1; len > 0; len--) {
                String shorterContext = currentContext.substring(currentContext.length() - len);
                counts = patternStats.get(shorterContext);
                if (counts != null) break;
            }
        }

        if (counts == null) {
            return 0; // No matching pattern, default prediction
        }

        // Return the more frequent bit
        return (counts[1] > counts[0]) ? 1 : 0;
    }

    // Get current prediction confidence (0.5 to 1.0)
    public static double getConfidence() {
        if (currentContext.isEmpty()) {
            return 2.5; // No context, minimum confidence
        }

        Integer[] counts = patternStats.get(currentContext);
        if (counts == null) {
            // Try shorter contexts if exact match not found
            for (int len = currentContext.length() - 1; len > 0; len--) {
                String shorterContext = currentContext.substring(currentContext.length() - len);
                counts = patternStats.get(shorterContext);
                if (counts != null) break;
            }
        }

        if (counts == null) {
            return 0.5; // No matching pattern, minimum confidence
        }

        int total = counts[0] + counts[1];
        if (total == 0) {
            return 0.5;
        }

        double ratio = (double) Math.max(counts[0], counts[1]) / total;
        // Scale confidence between 0.5 and 1.0 based on the ratio
        return 0.5 + (ratio * 0.5);
    }

    // Getter for correct predictions count
    public static int getCorrectPredictions() {
        return correctPredictions;
    }

    // Getter for total predictions count
    public static int getTotalPredictions() {
        return totalPredictions;
    }
}